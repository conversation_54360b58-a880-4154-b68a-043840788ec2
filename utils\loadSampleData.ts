import { sampleTransactions } from '@/data/sampleTransactions';
import { StorageManager } from '@/utils/storage';

export async function loadSampleData(): Promise<void> {
  try {
    // Check if data already exists
    const existingTransactions = await StorageManager.loadTransactions();
    
    // Only load sample data if no transactions exist
    if (existingTransactions.length === 0) {
      await StorageManager.saveTransactions(sampleTransactions);
      console.log('Sample data loaded successfully');
    } else {
      console.log('Existing data found, skipping sample data load');
    }
  } catch (error) {
    console.error('Failed to load sample data:', error);
  }
}