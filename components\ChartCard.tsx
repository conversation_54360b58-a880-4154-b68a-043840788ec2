import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { LineChart } from 'react-native-chart-kit';
import { Colors } from '@/constants/colors';
import { Typography } from '@/constants/typography';
import { DailySummary } from '@/types/transaction';

interface ChartCardProps {
  title: string;
  data: DailySummary[];
  type: 'sales' | 'payments' | 'net';
}

const screenWidth = Dimensions.get('window').width;

export default function ChartCard({ title, data, type }: ChartCardProps) {
  const getChartData = () => {
    // Safety check for data
    if (!data || data.length === 0) {
      return {
        labels: ['No Data'],
        datasets: [{
          data: [0],
          color: () => Colors.textSecondary,
          strokeWidth: 2,
        }]
      };
    }

    const labels = data.map(d => {
      const date = new Date(d.date);
      return `${date.getDate()}/${date.getMonth() + 1}`;
    });

    let datasets = [];

    switch (type) {
      case 'sales':
        datasets = [{
          data: data.map(d => d.sales),
          color: () => Colors.sale,
          strokeWidth: 2,
        }];
        break;
      case 'payments':
        datasets = [{
          data: data.map(d => d.payments),
          color: () => Colors.payment,
          strokeWidth: 2,
        }];
        break;
      case 'net':
        datasets = [{
          data: data.map(d => d.net),
          color: () => Colors.primary,
          strokeWidth: 2,
        }];
        break;
    }

    return {
      labels: labels.slice(-7), // Show last 7 days
      datasets,
    };
  };

  const chartConfig = {
    backgroundColor: Colors.cardBackground,
    backgroundGradientFrom: Colors.cardBackground,
    backgroundGradientTo: Colors.cardBackground,
    decimalPlaces: 0,
    color: (opacity = 1) => `rgba(0, 122, 255, ${opacity})`,
    labelColor: (opacity = 1) => `rgba(142, 142, 147, ${opacity})`,
    style: {
      borderRadius: 12,
    },
    propsForDots: {
      r: '4',
      strokeWidth: '2',
      stroke: Colors.cardBackground,
    },
  };

  const chartData = getChartData();

  if (chartData.datasets[0].data.every(val => val === 0)) {
    return (
      <View style={styles.card}>
        <Text style={styles.title}>{title}</Text>
        <View style={styles.noDataContainer}>
          <Text style={styles.noDataText}>No data available</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.card}>
      <Text style={styles.title}>{title}</Text>
      {(() => {
        try {
          return (
            <LineChart
              data={chartData}
              width={screenWidth - 64}
              height={200}
              chartConfig={chartConfig}
              bezier
              style={styles.chart}
              withInnerLines={false}
              withOuterLines={false}
              withShadow={false}
            />
          );
        } catch (error) {
          console.warn('Chart rendering error:', error);
          return (
            <View style={styles.noDataContainer}>
              <Text style={styles.noDataText}>Chart unavailable</Text>
            </View>
          );
        }
      })()}
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.cardBackground,
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  title: {
    fontSize: Typography.body,
    fontWeight: Typography.semibold,
    color: Colors.textPrimary,
    marginBottom: 12,
  },
  chart: {
    borderRadius: 8,
  },
  noDataContainer: {
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noDataText: {
    fontSize: Typography.caption,
    color: Colors.textSecondary,
  },
});