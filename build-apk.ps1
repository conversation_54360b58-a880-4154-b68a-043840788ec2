Write-Host "Building APK for Expense Tracker by V1Digital..." -ForegroundColor Green
Write-Host ""

Write-Host "Step 1: Installing EAS CLI..." -ForegroundColor Yellow
npm install -g @expo/cli
Write-Host ""

Write-Host "Step 2: Logging into Expo..." -ForegroundColor Yellow
Write-Host "You'll need to create an account at https://expo.dev if you don't have one" -ForegroundColor Cyan
npx expo login
Write-Host ""

Write-Host "Step 3: Building APK..." -ForegroundColor Yellow
npx eas build --platform android --profile preview
Write-Host ""

Write-Host "Build complete! Check the output above for the download link." -ForegroundColor Green
Read-Host "Press Enter to exit"
