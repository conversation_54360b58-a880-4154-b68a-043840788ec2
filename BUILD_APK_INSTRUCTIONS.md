# How to Build APK for Expense Tracker App

## Prerequisites

1. Create an Expo account at https://expo.dev
2. Install EAS CLI globally: `npm install -g @expo/cli`

## Step-by-Step Instructions

### 1. Login to Expo

```bash
npx expo login
```

### 2. Configure EAS Build

The `eas.json` file has already been created for you with the correct configuration.

### 3. Build the APK

```bash
npx eas build --platform android --profile preview
```

This will:

- Upload your code to Expo's build servers
- Build an APK file
- Provide you with a download link

### 4. Download and Install

- Once the build completes, you'll get a download link
- Download the APK file to your computer
- Transfer it to your Android phone
- Enable "Install from unknown sources" in your Android settings
- Install the APK

## Alternative: Using Expo Go (Immediate Testing)

For immediate testing without building an APK:

1. Install **Expo Go** from Google Play Store
2. Scan the QR code from your terminal when running `npx expo start`
3. The app will load directly on your phone

## Current App Features

✅ Blue footer with "Developed and managed by V1DIGITAL"
✅ Bold white text on blue background
✅ Footer appears on all screens:

- Dashboard
- Add Transaction
- Transactions List
- Settings
- Error pages

## App Configuration

- **App Name**: Expense Tracker by V1Digital
- **Package**: com.v1digital.expensetracker
- **Version**: 1.0.0
- **Icon**: Blue adaptive icon with V1Digital branding

## Recent Fixes (White Screen Issue)

✅ **Fixed white screen issue in standalone APK builds**

- Fixed `useFrameworkReady` hook to only run on web platform
- Added proper splash screen configuration
- Disabled new architecture temporarily for stability
- Added error boundary to catch and handle JavaScript errors
- Improved error handling in data loading

## Troubleshooting

If you encounter issues:

1. Make sure you're logged into Expo: `npx expo whoami`
2. Check your internet connection
3. Try the preview profile: `npx eas build --platform android --profile preview`
4. For local builds, you'll need Android Studio setup
5. If you still see a white screen, check device logs using `adb logcat`
