export interface Transaction {
  id: string;
  type: 'SALE' | 'PAYMENT';
  totalAmount: number;
  cashAmount: number;
  upiAmount: number;
  description?: string;
  date: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface TransactionFilters {
  dateRange?: {
    start: Date;
    end: Date;
  };
  type?: 'SALE' | 'PAYMENT' | 'ALL';
  paymentMethod?: 'CASH' | 'UPI' | 'BOTH';
  searchTerm?: string;
}

export interface Balance {
  cash: number;
  upi: number;
  total: number;
}

export interface DailySummary {
  date: string;
  sales: number;
  payments: number;
  net: number;
}