import AsyncStorage from '@react-native-async-storage/async-storage';
import { Transaction } from '@/types/transaction';

const STORAGE_KEYS = {
  TRANSACTIONS: 'lens_shop_transactions',
  SETTINGS: 'lens_shop_settings',
  BACKUP: 'lens_shop_backup',
};

export class StorageManager {
  static async saveTransactions(transactions: Transaction[]): Promise<void> {
    try {
      const serialized = JSON.stringify(transactions);
      await AsyncStorage.setItem(STORAGE_KEYS.TRANSACTIONS, serialized);
    } catch (error) {
      console.error('Failed to save transactions:', error);
      throw new Error('Failed to save data');
    }
  }

  static async loadTransactions(): Promise<Transaction[]> {
    try {
      const serialized = await AsyncStorage.getItem(STORAGE_KEYS.TRANSACTIONS);
      if (!serialized) return [];
      
      const parsed = JSON.parse(serialized);
      return parsed.map((t: any) => ({
        ...t,
        date: new Date(t.date),
        createdAt: new Date(t.createdAt),
        updatedAt: new Date(t.updatedAt),
      }));
    } catch (error) {
      console.error('Failed to load transactions:', error);
      return [];
    }
  }

  static async clearAllData(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        STORAGE_KEYS.TRANSACTIONS,
        STORAGE_KEYS.SETTINGS,
        STORAGE_KEYS.BACKUP,
      ]);
    } catch (error) {
      console.error('Failed to clear data:', error);
      throw new Error('Failed to clear data');
    }
  }

  static async createBackup(): Promise<string> {
    try {
      const transactions = await this.loadTransactions();
      const backup = {
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        data: transactions,
      };
      
      const backupString = JSON.stringify(backup);
      await AsyncStorage.setItem(STORAGE_KEYS.BACKUP, backupString);
      return backupString;
    } catch (error) {
      console.error('Failed to create backup:', error);
      throw new Error('Failed to create backup');
    }
  }

  static generateUniqueId(): string {
    return `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}