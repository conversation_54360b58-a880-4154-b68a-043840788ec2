import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import { Transaction } from '@/types/transaction';
import { Colors, getTransactionColor } from '@/constants/colors';
import { Typography } from '@/constants/typography';
import { TrendingUp, TrendingDown } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';

interface TransactionCardProps {
  transaction: Transaction;
  onPress?: () => void;
}

export default function TransactionCard({ transaction, onPress }: TransactionCardProps) {
  const handlePress = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    onPress?.();
  };

  const formatAmount = (value: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2,
    }).format(value);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(date));
  };

  const transactionColor = getTransactionColor(transaction.type);
  const Icon = transaction.type === 'SALE' ? TrendingUp : TrendingDown;

  return (
    <TouchableOpacity style={styles.card} onPress={handlePress} activeOpacity={0.7}>
      <View style={styles.header}>
        <View style={styles.iconContainer}>
          <Icon size={20} color={transactionColor} />
        </View>
        <View style={styles.headerText}>
          <Text style={styles.type}>
            {transaction.type === 'SALE' ? 'Sale' : 'Payment'}
          </Text>
          <Text style={styles.date}>{formatDate(transaction.date)}</Text>
        </View>
        <Text style={[styles.amount, { color: transactionColor }]}>
          {formatAmount(transaction.totalAmount)}
        </Text>
      </View>
      
      {transaction.description && (
        <Text style={styles.description} numberOfLines={2}>
          {transaction.description}
        </Text>
      )}
      
      <View style={styles.paymentMethods}>
        {transaction.cashAmount > 0 && (
          <View style={styles.paymentMethod}>
            <Text style={styles.paymentLabel}>Cash:</Text>
            <Text style={styles.paymentAmount}>
              {formatAmount(transaction.cashAmount)}
            </Text>
          </View>
        )}
        {transaction.upiAmount > 0 && (
          <View style={styles.paymentMethod}>
            <Text style={styles.paymentLabel}>UPI:</Text>
            <Text style={styles.paymentAmount}>
              {formatAmount(transaction.upiAmount)}
            </Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.cardBackground,
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 12,
    borderRadius: 12,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.background,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  headerText: {
    flex: 1,
  },
  type: {
    fontSize: Typography.body,
    fontWeight: Typography.semibold,
    color: Colors.textPrimary,
  },
  date: {
    fontSize: Typography.caption,
    color: Colors.textSecondary,
    marginTop: 2,
  },
  amount: {
    fontSize: Typography.amount,
    fontWeight: Typography.bold,
    fontFamily: Typography.monospace,
  },
  description: {
    fontSize: Typography.caption,
    color: Colors.textSecondary,
    marginBottom: 8,
    lineHeight: Typography.normal * Typography.caption,
  },
  paymentMethods: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: Colors.separator,
  },
  paymentMethod: {
    flex: 1,
    alignItems: 'center',
  },
  paymentLabel: {
    fontSize: Typography.small,
    color: Colors.textSecondary,
    marginBottom: 2,
  },
  paymentAmount: {
    fontSize: Typography.caption,
    fontWeight: Typography.medium,
    color: Colors.textPrimary,
    fontFamily: Typography.monospace,
  },
});