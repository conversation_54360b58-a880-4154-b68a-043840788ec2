import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors } from '@/constants/colors';
import { Typography } from '@/constants/typography';

interface BalanceCardProps {
  title: string;
  amount: number;
  type: 'cash' | 'upi' | 'total';
}

export default function BalanceCard({ title, amount, type }: BalanceCardProps) {
  const getCardStyle = () => {
    switch (type) {
      case 'total':
        return [styles.card, styles.totalCard];
      default:
        return styles.card;
    }
  };

  const getAmountColor = () => {
    if (type === 'total') {
      return amount >= 0 ? Colors.success : Colors.error;
    }
    return Colors.textPrimary;
  };

  const formatAmount = (value: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2,
    }).format(value);
  };

  return (
    <View style={getCardStyle()}>
      <Text style={styles.title}>{title}</Text>
      <Text style={[styles.amount, { color: getAmountColor() }]}>
        {formatAmount(amount)}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.cardBackground,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  totalCard: {
    borderWidth: 2,
    borderColor: Colors.primary,
  },
  title: {
    fontSize: Typography.caption,
    fontWeight: Typography.medium,
    color: Colors.textSecondary,
    marginBottom: 4,
  },
  amount: {
    fontSize: Typography.amount,
    fontWeight: Typography.bold,
    fontFamily: Typography.monospace,
  },
});