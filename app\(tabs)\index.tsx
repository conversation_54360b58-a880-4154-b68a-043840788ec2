import React, { useEffect, useRef } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  RefreshControl,
  Alert,
  Platform 
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTransactions } from '@/contexts/TransactionContext';
import BalanceCard from '@/components/BalanceCard';
import TransactionCard from '@/components/TransactionCard';
import ChartCard from '@/components/ChartCard';
import V1DigitalFooter from '@/components/V1DigitalFooter';
import { SafeComponent } from '@/components/SafeComponent';
import { Colors } from '@/constants/colors';
import { Typography } from '@/constants/typography';
import { loadSampleData } from '@/utils/loadSampleData';
import * as Haptics from 'expo-haptics';

export default function DashboardScreen() {
  const { 
    state, 
    getBalance, 
    getFilteredTransactions, 
    getDailySummary 
  } = useTransactions();
  
  const scrollViewRef = useRef<ScrollView>(null);
  const [refreshing, setRefreshing] = React.useState(false);

  const balance = getBalance();
  const recentTransactions = getFilteredTransactions().slice(0, 10);
  const dailySummary = getDailySummary(7);

  // Load sample data on first launch
  useEffect(() => {
    if (!state.loading && state.transactions.length === 0) {
      loadSampleData();
    }
  }, [state.loading, state.transactions.length]);

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    // Simulate refresh delay
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  }, []);

  const handleTransactionPress = () => {
    Alert.alert('Transaction Details', 'Transaction details would be shown here');
  };

  if (state.loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (state.error) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{state.error}</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        ref={scrollViewRef}
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.header}>
          <Text style={styles.title}>Lens Shop Dashboard</Text>
          <Text style={styles.subtitle}>
            {new Date().toLocaleDateString('en-IN', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric',
            })}
          </Text>
        </View>

        <View style={styles.balanceSection}>
          <BalanceCard title="Total Balance" amount={balance.total} type="total" />
          <View style={styles.balanceRow}>
            <View style={styles.balanceHalf}>
              <BalanceCard title="Cash Balance" amount={balance.cash} type="cash" />
            </View>
            <View style={styles.balanceHalf}>
              <BalanceCard title="UPI Balance" amount={balance.upi} type="upi" />
            </View>
          </View>
        </View>

        <View style={styles.chartsSection}>
          <SafeComponent componentName="Sales Chart">
            <ChartCard title="Sales Trend (7 days)" data={dailySummary} type="sales" />
          </SafeComponent>
          <SafeComponent componentName="Net Income Chart">
            <ChartCard title="Net Income (7 days)" data={dailySummary} type="net" />
          </SafeComponent>
        </View>

        <View style={styles.transactionsSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Transactions</Text>
            <Text style={styles.sectionCount}>
              {recentTransactions.length} of {state.transactions.length}
            </Text>
          </View>
          
          {recentTransactions.length === 0 ? (
            <View style={styles.emptyState}>
              <Text style={styles.emptyText}>No transactions yet</Text>
              <Text style={styles.emptySubtext}>
                Add your first transaction to get started
              </Text>
            </View>
          ) : (
            recentTransactions.map((transaction) => (
              <TransactionCard
                key={transaction.id}
                transaction={transaction}
                onPress={handleTransactionPress}
              />
            ))
          )}
        </View>

        <V1DigitalFooter />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100,
  },
  header: {
    padding: 20,
    paddingBottom: 12,
  },
  title: {
    fontSize: Typography.headerLarge,
    fontWeight: Typography.bold,
    color: Colors.textPrimary,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: Typography.caption,
    color: Colors.textSecondary,
  },
  balanceSection: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  balanceRow: {
    flexDirection: 'row',
    gap: 12,
  },
  balanceHalf: {
    flex: 1,
  },
  chartsSection: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  transactionsSection: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: Typography.header,
    fontWeight: Typography.semibold,
    color: Colors.textPrimary,
  },
  sectionCount: {
    fontSize: Typography.caption,
    color: Colors.textSecondary,
  },
  emptyState: {
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: Typography.body,
    fontWeight: Typography.medium,
    color: Colors.textSecondary,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: Typography.caption,
    color: Colors.textTertiary,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: Typography.body,
    color: Colors.textSecondary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: Typography.body,
    color: Colors.error,
    textAlign: 'center',
  },
});