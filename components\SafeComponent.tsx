import React, { ReactNode } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors } from '@/constants/colors';
import { Typography } from '@/constants/typography';

interface SafeComponentProps {
  children: ReactNode;
  fallback?: ReactNode;
  componentName?: string;
}

export function SafeComponent({ children, fallback, componentName = 'Component' }: SafeComponentProps) {
  try {
    return <>{children}</>;
  } catch (error) {
    console.warn(`${componentName} rendering error:`, error);
    
    if (fallback) {
      return <>{fallback}</>;
    }
    
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>
          {componentName} unavailable
        </Text>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  errorContainer: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.cardBackground,
    borderRadius: 8,
    margin: 8,
  },
  errorText: {
    fontSize: Typography.caption,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
});
