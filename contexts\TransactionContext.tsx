import React, { create<PERSON>ontext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { Transaction, TransactionFilters, Balance, DailySummary } from '@/types/transaction';
import { StorageManager } from '@/utils/storage';

interface TransactionState {
  transactions: Transaction[];
  loading: boolean;
  error: string | null;
  filters: TransactionFilters;
}

type TransactionAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_TRANSACTIONS'; payload: Transaction[] }
  | { type: 'ADD_TRANSACTION'; payload: Transaction }
  | { type: 'UPDATE_TRANSACTION'; payload: Transaction }
  | { type: 'DELETE_TRANSACTION'; payload: string }
  | { type: 'SET_FILTERS'; payload: TransactionFilters }
  | { type: 'CLEAR_FILTERS' };

const initialState: TransactionState = {
  transactions: [],
  loading: true,
  error: null,
  filters: {},
};

function transactionReducer(state: TransactionState, action: TransactionAction): TransactionState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    case 'SET_TRANSACTIONS':
      return { ...state, transactions: action.payload, loading: false, error: null };
    case 'ADD_TRANSACTION':
      return { 
        ...state, 
        transactions: [action.payload, ...state.transactions],
        error: null 
      };
    case 'UPDATE_TRANSACTION':
      return {
        ...state,
        transactions: state.transactions.map(t => 
          t.id === action.payload.id ? action.payload : t
        ),
        error: null
      };
    case 'DELETE_TRANSACTION':
      return {
        ...state,
        transactions: state.transactions.filter(t => t.id !== action.payload),
        error: null
      };
    case 'SET_FILTERS':
      return { ...state, filters: action.payload };
    case 'CLEAR_FILTERS':
      return { ...state, filters: {} };
    default:
      return state;
  }
}

interface TransactionContextType {
  state: TransactionState;
  addTransaction: (transaction: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateTransaction: (transaction: Transaction) => Promise<void>;
  deleteTransaction: (id: string) => Promise<void>;
  getBalance: () => Balance;
  getFilteredTransactions: () => Transaction[];
  getDailySummary: (days: number) => DailySummary[];
  setFilters: (filters: TransactionFilters) => void;
  clearFilters: () => void;
  exportData: () => Promise<string>;
  importData: (data: string) => Promise<void>;
}

const TransactionContext = createContext<TransactionContextType | undefined>(undefined);

export function TransactionProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(transactionReducer, initialState);

  useEffect(() => {
    loadTransactions();
  }, []);

  useEffect(() => {
    if (!state.loading && state.transactions.length > 0) {
      StorageManager.saveTransactions(state.transactions);
    }
  }, [state.transactions, state.loading]);

  const loadTransactions = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const transactions = await StorageManager.loadTransactions();
      dispatch({ type: 'SET_TRANSACTIONS', payload: transactions });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to load transactions' });
    }
  };

  const addTransaction = async (transactionData: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      const now = new Date();
      const newTransaction: Transaction = {
        ...transactionData,
        id: StorageManager.generateUniqueId(),
        createdAt: now,
        updatedAt: now,
      };
      
      dispatch({ type: 'ADD_TRANSACTION', payload: newTransaction });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to add transaction' });
    }
  };

  const updateTransaction = async (transaction: Transaction) => {
    try {
      const updatedTransaction = {
        ...transaction,
        updatedAt: new Date(),
      };
      dispatch({ type: 'UPDATE_TRANSACTION', payload: updatedTransaction });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to update transaction' });
    }
  };

  const deleteTransaction = async (id: string) => {
    try {
      dispatch({ type: 'DELETE_TRANSACTION', payload: id });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to delete transaction' });
    }
  };

  const getBalance = (): Balance => {
    const totals = state.transactions.reduce(
      (acc, transaction) => {
        const multiplier = transaction.type === 'SALE' ? 1 : -1;
        return {
          cash: acc.cash + (transaction.cashAmount * multiplier),
          upi: acc.upi + (transaction.upiAmount * multiplier),
        };
      },
      { cash: 0, upi: 0 }
    );

    return {
      ...totals,
      total: totals.cash + totals.upi,
    };
  };

  const getFilteredTransactions = (): Transaction[] => {
    let filtered = [...state.transactions];

    if (state.filters.type && state.filters.type !== 'ALL') {
      filtered = filtered.filter(t => t.type === state.filters.type);
    }

    if (state.filters.dateRange) {
      filtered = filtered.filter(t => {
        const transactionDate = new Date(t.date);
        return transactionDate >= state.filters.dateRange!.start && 
               transactionDate <= state.filters.dateRange!.end;
      });
    }

    if (state.filters.paymentMethod && state.filters.paymentMethod !== 'BOTH') {
      filtered = filtered.filter(t => {
        if (state.filters.paymentMethod === 'CASH') {
          return t.cashAmount > 0;
        } else {
          return t.upiAmount > 0;
        }
      });
    }

    if (state.filters.searchTerm) {
      const searchLower = state.filters.searchTerm.toLowerCase();
      filtered = filtered.filter(t => 
        t.description?.toLowerCase().includes(searchLower) ||
        t.totalAmount.toString().includes(searchLower)
      );
    }

    return filtered.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  };

  const getDailySummary = (days: number): DailySummary[] => {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - days);

    const dailyData: { [key: string]: DailySummary } = {};

    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      const dateStr = d.toISOString().split('T')[0];
      dailyData[dateStr] = {
        date: dateStr,
        sales: 0,
        payments: 0,
        net: 0,
      };
    }

    state.transactions.forEach(transaction => {
      const dateStr = new Date(transaction.date).toISOString().split('T')[0];
      if (dailyData[dateStr]) {
        if (transaction.type === 'SALE') {
          dailyData[dateStr].sales += transaction.totalAmount;
        } else {
          dailyData[dateStr].payments += transaction.totalAmount;
        }
        dailyData[dateStr].net = dailyData[dateStr].sales - dailyData[dateStr].payments;
      }
    });

    return Object.values(dailyData);
  };

  const setFilters = (filters: TransactionFilters) => {
    dispatch({ type: 'SET_FILTERS', payload: filters });
  };

  const clearFilters = () => {
    dispatch({ type: 'CLEAR_FILTERS' });
  };

  const exportData = async (): Promise<string> => {
    return await StorageManager.createBackup();
  };

  const importData = async (data: string) => {
    try {
      const parsed = JSON.parse(data);
      if (parsed.data && Array.isArray(parsed.data)) {
        dispatch({ type: 'SET_TRANSACTIONS', payload: parsed.data });
      } else {
        throw new Error('Invalid data format');
      }
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to import data' });
    }
  };

  const value: TransactionContextType = {
    state,
    addTransaction,
    updateTransaction,
    deleteTransaction,
    getBalance,
    getFilteredTransactions,
    getDailySummary,
    setFilters,
    clearFilters,
    exportData,
    importData,
  };

  return (
    <TransactionContext.Provider value={value}>
      {children}
    </TransactionContext.Provider>
  );
}

export function useTransactions() {
  const context = useContext(TransactionContext);
  if (context === undefined) {
    throw new Error('useTransactions must be used within a TransactionProvider');
  }
  return context;
}