import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTransactions } from '@/contexts/TransactionContext';
import { Colors, getTransactionColor } from '@/constants/colors';
import { Typography } from '@/constants/typography';
import { Plus, Calendar } from 'lucide-react-native';
import V1DigitalFooter from '@/components/V1DigitalFooter';
import * as Haptics from 'expo-haptics';

export default function AddTransactionScreen() {
  const { addTransaction } = useTransactions();
  
  const [transactionType, setTransactionType] = useState<'SALE' | 'PAYMENT'>('SALE');
  const [totalAmount, setTotalAmount] = useState('');
  const [cashAmount, setCashAmount] = useState('');
  const [upiAmount, setUpiAmount] = useState('');
  const [description, setDescription] = useState('');
  const [date, setDate] = useState(new Date());
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    try {
      // Validation
      if (!totalAmount || parseFloat(totalAmount) <= 0) {
        Alert.alert('Error', 'Please enter a valid total amount');
        return;
      }

      const total = parseFloat(totalAmount);
      const cash = parseFloat(cashAmount) || 0;
      const upi = parseFloat(upiAmount) || 0;

      if (Math.abs(total - (cash + upi)) > 0.01) {
        Alert.alert('Error', 'Cash + UPI amounts must equal the total amount');
        return;
      }

      if (description.length > 200) {
        Alert.alert('Error', 'Description must be less than 200 characters');
        return;
      }

      setLoading(true);

      if (Platform.OS !== 'web') {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      }

      await addTransaction({
        type: transactionType,
        totalAmount: total,
        cashAmount: cash,
        upiAmount: upi,
        description: description.trim() || undefined,
        date,
      });

      // Reset form
      setTotalAmount('');
      setCashAmount('');
      setUpiAmount('');
      setDescription('');
      setDate(new Date());

      Alert.alert('Success', 'Transaction added successfully!');
    } catch (error) {
      Alert.alert('Error', 'Failed to add transaction. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const updateAmountSplit = (field: 'total' | 'cash' | 'upi', value: string) => {
    const numValue = parseFloat(value) || 0;

    switch (field) {
      case 'total':
        setTotalAmount(value);
        // Auto-split equally if no specific amounts are set
        if (!cashAmount && !upiAmount && numValue > 0) {
          const half = (numValue / 2).toFixed(2);
          setCashAmount(half);
          setUpiAmount(half);
        }
        break;
      case 'cash':
        setCashAmount(value);
        if (totalAmount) {
          const total = parseFloat(totalAmount);
          const remaining = (total - numValue).toFixed(2);
          setUpiAmount(remaining);
        }
        break;
      case 'upi':
        setUpiAmount(value);
        if (totalAmount) {
          const total = parseFloat(totalAmount);
          const remaining = (total - numValue).toFixed(2);
          setCashAmount(remaining);
        }
        break;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <Text style={styles.title}>Add Transaction</Text>
          <Text style={styles.subtitle}>Enter transaction details</Text>
        </View>

        <View style={styles.form}>
          {/* Transaction Type */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Transaction Type</Text>
            <View style={styles.typeButtons}>
              <TouchableOpacity
                style={[
                  styles.typeButton,
                  transactionType === 'SALE' && styles.typeButtonActive,
                  { borderColor: Colors.sale }
                ]}
                onPress={() => setTransactionType('SALE')}
              >
                <Text style={[
                  styles.typeButtonText,
                  transactionType === 'SALE' && { color: Colors.sale }
                ]}>
                  Sale (Income)
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.typeButton,
                  transactionType === 'PAYMENT' && styles.typeButtonActive,
                  { borderColor: Colors.payment }
                ]}
                onPress={() => setTransactionType('PAYMENT')}
              >
                <Text style={[
                  styles.typeButtonText,
                  transactionType === 'PAYMENT' && { color: Colors.payment }
                ]}>
                  Payment (Expense)
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Amount Fields */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Amount Details</Text>
            
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Total Amount *</Text>
              <TextInput
                style={styles.input}
                value={totalAmount}
                onChangeText={(value) => updateAmountSplit('total', value)}
                placeholder="0.00"
                keyboardType="numeric"
                maxLength={10}
              />
            </View>

            <View style={styles.amountSplit}>
              <View style={styles.splitInput}>
                <Text style={styles.inputLabel}>Cash Amount</Text>
                <TextInput
                  style={styles.input}
                  value={cashAmount}
                  onChangeText={(value) => updateAmountSplit('cash', value)}
                  placeholder="0.00"
                  keyboardType="numeric"
                  maxLength={10}
                />
              </View>
              <View style={styles.splitInput}>
                <Text style={styles.inputLabel}>UPI Amount</Text>
                <TextInput
                  style={styles.input}
                  value={upiAmount}
                  onChangeText={(value) => updateAmountSplit('upi', value)}
                  placeholder="0.00"
                  keyboardType="numeric"
                  maxLength={10}
                />
              </View>
            </View>
          </View>

          {/* Description */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Description (Optional)</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={description}
              onChangeText={setDescription}
              placeholder="Enter transaction description..."
              multiline
              numberOfLines={3}
              maxLength={200}
            />
            <Text style={styles.characterCount}>
              {description.length}/200 characters
            </Text>
          </View>

          {/* Date */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Date</Text>
            <TouchableOpacity style={styles.dateButton}>
              <Calendar size={20} color={Colors.textSecondary} />
              <Text style={styles.dateText}>
                {date.toLocaleDateString('en-IN', {
                  day: '2-digit',
                  month: 'long',
                  year: 'numeric',
                })}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Submit Button */}
          <TouchableOpacity
            style={[
              styles.submitButton,
              { backgroundColor: getTransactionColor(transactionType) },
              loading && styles.submitButtonDisabled,
            ]}
            onPress={handleSubmit}
            disabled={loading}
          >
            <Plus size={20} color={Colors.cardBackground} />
            <Text style={styles.submitButtonText}>
              {loading ? 'Adding...' : `Add ${transactionType === 'SALE' ? 'Sale' : 'Payment'}`}
            </Text>
          </TouchableOpacity>
        </View>

        <V1DigitalFooter />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100,
  },
  header: {
    padding: 20,
    paddingBottom: 12,
  },
  title: {
    fontSize: Typography.headerLarge,
    fontWeight: Typography.bold,
    color: Colors.textPrimary,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: Typography.caption,
    color: Colors.textSecondary,
  },
  form: {
    padding: 20,
    gap: 24,
  },
  section: {
    gap: 12,
  },
  sectionTitle: {
    fontSize: Typography.body,
    fontWeight: Typography.semibold,
    color: Colors.textPrimary,
  },
  typeButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  typeButton: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.border,
    backgroundColor: Colors.cardBackground,
    alignItems: 'center',
  },
  typeButtonActive: {
    backgroundColor: Colors.background,
  },
  typeButtonText: {
    fontSize: Typography.body,
    fontWeight: Typography.medium,
    color: Colors.textSecondary,
  },
  inputGroup: {
    gap: 8,
  },
  inputLabel: {
    fontSize: Typography.caption,
    fontWeight: Typography.medium,
    color: Colors.textSecondary,
  },
  input: {
    backgroundColor: Colors.inputBackground,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.border,
    fontSize: Typography.body,
    color: Colors.textPrimary,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  amountSplit: {
    flexDirection: 'row',
    gap: 12,
  },
  splitInput: {
    flex: 1,
    gap: 8,
  },
  characterCount: {
    fontSize: Typography.small,
    color: Colors.textTertiary,
    textAlign: 'right',
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.inputBackground,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.border,
    gap: 12,
  },
  dateText: {
    fontSize: Typography.body,
    color: Colors.textPrimary,
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    gap: 8,
    marginTop: 12,
  },
  submitButtonDisabled: {
    opacity: 0.5,
  },
  submitButtonText: {
    fontSize: Typography.body,
    fontWeight: Typography.semibold,
    color: Colors.cardBackground,
  },
});