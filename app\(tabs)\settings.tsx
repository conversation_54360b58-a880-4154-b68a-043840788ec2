import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Share,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTransactions } from '@/contexts/TransactionContext';
import { Colors } from '@/constants/colors';
import { Typography } from '@/constants/typography';
import { Download, Upload, Trash2, Database, FileText, RefreshCw, ChartBar as BarChart3 } from 'lucide-react-native';
import V1DigitalFooter from '@/components/V1DigitalFooter';
import { StorageManager } from '@/utils/storage';

export default function SettingsScreen() {
  const { state, exportData, importData } = useTransactions();
  const [loading, setLoading] = useState(false);

  const handleExportData = async () => {
    try {
      setLoading(true);
      const exportString = await exportData();
      
      if (Share.share) {
        await Share.share({
          message: exportString,
          title: 'Lens Shop Transactions Export',
        });
      } else {
        Alert.alert('Export Data', 'Export functionality not available on this platform');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to export data');
    } finally {
      setLoading(false);
    }
  };

  const handleImportData = () => {
    Alert.alert(
      'Import Data',
      'Import functionality would allow you to restore data from a backup file.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'OK' }
      ]
    );
  };

  const handleClearData = () => {
    Alert.alert(
      'Clear All Data',
      'This will permanently delete all transactions. This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Delete All', 
          style: 'destructive',
          onPress: async () => {
            try {
              await StorageManager.clearAllData();
              Alert.alert('Success', 'All data has been cleared');
            } catch (error) {
              Alert.alert('Error', 'Failed to clear data');
            }
          }
        }
      ]
    );
  };

  const handleCreateBackup = async () => {
    try {
      setLoading(true);
      await StorageManager.createBackup();
      Alert.alert('Success', 'Backup created successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to create backup');
    } finally {
      setLoading(false);
    }
  };

  const getStorageInfo = () => {
    return {
      totalTransactions: state.transactions.length,
      totalSales: state.transactions.filter(t => t.type === 'SALE').length,
      totalPayments: state.transactions.filter(t => t.type === 'PAYMENT').length,
      oldestTransaction: state.transactions.length > 0 
        ? new Date(Math.min(...state.transactions.map(t => new Date(t.date).getTime())))
        : null,
      newestTransaction: state.transactions.length > 0
        ? new Date(Math.max(...state.transactions.map(t => new Date(t.date).getTime())))
        : null,
    };
  };

  const storageInfo = getStorageInfo();

  const SettingItem = ({ 
    icon: Icon, 
    title, 
    subtitle, 
    onPress, 
    destructive = false,
    disabled = false 
  }: {
    icon: any;
    title: string;
    subtitle?: string;
    onPress: () => void;
    destructive?: boolean;
    disabled?: boolean;
  }) => (
    <TouchableOpacity
      style={[styles.settingItem, disabled && styles.settingItemDisabled]}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.7}
    >
      <View style={styles.settingIcon}>
        <Icon 
          size={20} 
          color={destructive ? Colors.error : Colors.primary} 
        />
      </View>
      <View style={styles.settingContent}>
        <Text style={[
          styles.settingTitle,
          destructive && { color: Colors.error },
          disabled && { color: Colors.textTertiary }
        ]}>
          {title}
        </Text>
        {subtitle && (
          <Text style={styles.settingSubtitle}>{subtitle}</Text>
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <Text style={styles.title}>Settings</Text>
          <Text style={styles.subtitle}>Manage your data and preferences</Text>
        </View>

        {/* Storage Info */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Storage Information</Text>
          <View style={styles.infoCard}>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Total Transactions:</Text>
              <Text style={styles.infoValue}>{storageInfo.totalTransactions}</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Sales:</Text>
              <Text style={styles.infoValue}>{storageInfo.totalSales}</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Payments:</Text>
              <Text style={styles.infoValue}>{storageInfo.totalPayments}</Text>
            </View>
            {storageInfo.oldestTransaction && (
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Date Range:</Text>
                <Text style={styles.infoValue}>
                  {storageInfo.oldestTransaction.toLocaleDateString('en-IN')} - {' '}
                  {storageInfo.newestTransaction?.toLocaleDateString('en-IN')}
                </Text>
              </View>
            )}
          </View>
        </View>

        {/* Data Management */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Data Management</Text>
          <View style={styles.settingsGroup}>
            <SettingItem
              icon={Download}
              title="Export Data"
              subtitle="Export all transactions to backup file"
              onPress={handleExportData}
              disabled={loading || state.transactions.length === 0}
            />
            <SettingItem
              icon={Upload}
              title="Import Data"
              subtitle="Restore transactions from backup file"
              onPress={handleImportData}
              disabled={loading}
            />
            <SettingItem
              icon={Database}
              title="Create Backup"
              subtitle="Create a backup of current data"
              onPress={handleCreateBackup}
              disabled={loading || state.transactions.length === 0}
            />
          </View>
        </View>

        {/* Statistics */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Statistics & Reports</Text>
          <View style={styles.settingsGroup}>
            <SettingItem
              icon={BarChart3}
              title="Transaction Analytics"
              subtitle="View detailed transaction analytics"
              onPress={() => Alert.alert('Coming Soon', 'Advanced analytics will be available in a future update')}
            />
            <SettingItem
              icon={FileText}
              title="Generate Report"
              subtitle="Create monthly or yearly reports"
              onPress={() => Alert.alert('Coming Soon', 'Report generation will be available in a future update')}
            />
          </View>
        </View>

        {/* Maintenance */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Maintenance</Text>
          <View style={styles.settingsGroup}>
            <SettingItem
              icon={RefreshCw}
              title="Refresh Data"
              subtitle="Reload all transactions from storage"
              onPress={() => {
                Alert.alert('Success', 'Data refreshed successfully');
              }}
            />
            <SettingItem
              icon={Trash2}
              title="Clear All Data"
              subtitle="Permanently delete all transactions"
              onPress={handleClearData}
              destructive={true}
              disabled={loading || state.transactions.length === 0}
            />
          </View>
        </View>

        {/* App Info */}
        <View style={styles.section}>
          <View style={styles.appInfo}>
            <Text style={styles.appName}>Lens Shop Transaction Manager</Text>
            <Text style={styles.appVersion}>Version 1.0.0</Text>
            <Text style={styles.appDescription}>
              Offline-first transaction management for lens shops with advanced analytics and reporting.
            </Text>
          </View>
        </View>

        <V1DigitalFooter />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100,
  },
  header: {
    padding: 20,
    paddingBottom: 12,
  },
  title: {
    fontSize: Typography.headerLarge,
    fontWeight: Typography.bold,
    color: Colors.textPrimary,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: Typography.caption,
    color: Colors.textSecondary,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: Typography.body,
    fontWeight: Typography.semibold,
    color: Colors.textPrimary,
    marginBottom: 12,
    marginHorizontal: 20,
  },
  infoCard: {
    backgroundColor: Colors.cardBackground,
    marginHorizontal: 20,
    padding: 16,
    borderRadius: 12,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  infoLabel: {
    fontSize: Typography.caption,
    color: Colors.textSecondary,
  },
  infoValue: {
    fontSize: Typography.caption,
    fontWeight: Typography.medium,
    color: Colors.textPrimary,
  },
  settingsGroup: {
    backgroundColor: Colors.cardBackground,
    marginHorizontal: 20,
    borderRadius: 12,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.separator,
  },
  settingItemDisabled: {
    opacity: 0.5,
  },
  settingIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.background,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: Typography.body,
    fontWeight: Typography.medium,
    color: Colors.textPrimary,
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: Typography.caption,
    color: Colors.textSecondary,
  },
  appInfo: {
    alignItems: 'center',
    padding: 20,
  },
  appName: {
    fontSize: Typography.body,
    fontWeight: Typography.semibold,
    color: Colors.textPrimary,
    marginBottom: 4,
  },
  appVersion: {
    fontSize: Typography.caption,
    color: Colors.textSecondary,
    marginBottom: 8,
  },
  appDescription: {
    fontSize: Typography.caption,
    color: Colors.textTertiary,
    textAlign: 'center',
    lineHeight: Typography.normal * Typography.caption,
  },
});